#!/usr/bin/env python3
from abc import ABC, abstractmethod
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
import argparse
import asyncio
import json
import os
import sys
import traceback

from llm_router.analytics.config import config
from llm_router.analytics.prisma_client import (
    AnalyticsPrismaClient,
    get_analytics_prisma_client,
)
from llm_router.logging_utils import get_logger

"""
Analytics Summary Generator Module.

Enterprise-level table summary system with modular architecture.
Implements Strategy Pattern, Dependency Injection, and Business Intelligence.
"""

sys.path.append(str(Path(__file__).parent.parent.parent))
logger = get_logger(__name__)


class SummaryQueryManager:
    """SQL template management for table summaries."""

    def __init__(self):
        """Initialize summary query manager with SQL templates."""
        logger.debug("SUMMARY_QUERY_MANAGER: initializing SQL template manager")
        self.templates = self._load_summary_templates()
        logger.info(
            f"SUMMARY_QUERY_MANAGER: loaded {len(self.templates)} SQL templates"
        )

    def _load_summary_templates(self) -> Dict[str, str]:
        """Load SQL query templates for table summaries."""
        return {
            "models_summary": '\n                SELECT \n                    m.name as model_name,\n                    COUNT(*) as total_requests,\n                    COUNT(CASE WHEN rl.success = true THEN 1 END) as successful_requests,\n                    ROUND((COUNT(CASE WHEN rl.success = true THEN 1 END) * 100.0 / COUNT(*))::numeric, 2) as success_rate,\n                    ROUND(AVG(rl.actual_response_time)::numeric, 3) as avg_response_time,\n                    ROUND(AVG(rl.prediction_accuracy)::numeric, 3) as avg_prediction_accuracy,\n                    COUNT(DISTINCT pred.selected_deployment_id) as unique_endpoints,\n                    MIN(pl.created_at) as first_request,\n                    MAX(pl.created_at) as last_request\n                FROM "LiteLLM_PromptLogs" pl\n                LEFT JOIN "LiteLLM_Models" m ON pl.model_id = m.id\n                LEFT JOIN "LiteLLM_ResponseLogs" rl ON pl.request_id = rl.request_id\n                LEFT JOIN "LiteLLM_PredictorLogs" pred ON pl.request_id = pred.request_id\n                WHERE 1=1 {where_conditions}\n                  AND pred.selected_deployment_id IS NOT NULL\n                GROUP BY m.name\n                ORDER BY total_requests DESC\n            ',
            "models_summary_full": '\n                SELECT \n                    m.name as model_name,\n                    pred.selected_deployment_id as endpoint,\n                    COUNT(*) as total_requests,\n                    COUNT(CASE WHEN rl.success = true THEN 1 END) as successful_requests,\n                    ROUND((COUNT(CASE WHEN rl.success = true THEN 1 END) * 100.0 / COUNT(*))::numeric, 2) as success_rate,\n                    ROUND(AVG(rl.actual_response_time)::numeric, 3) as avg_response_time,\n                    ROUND(MIN(rl.actual_response_time)::numeric, 3) as min_response_time,\n                    ROUND(MAX(rl.actual_response_time)::numeric, 3) as max_response_time,\n                    ROUND(AVG(rl.prediction_accuracy)::numeric, 3) as avg_prediction_accuracy,\n                    ROUND(AVG(pred.predicted_time)::numeric, 3) as avg_predicted_time,\n                    COUNT(CASE WHEN pred.least_busy_available = true THEN 1 END) as least_busy_selections,\n                    MIN(pl.created_at) as first_request,\n                    MAX(pl.created_at) as last_request\n                FROM "LiteLLM_PromptLogs" pl\n                LEFT JOIN "LiteLLM_Models" m ON pl.model_id = m.id\n                LEFT JOIN "LiteLLM_ResponseLogs" rl ON pl.request_id = rl.request_id\n                LEFT JOIN "LiteLLM_PredictorLogs" pred ON pl.request_id = pred.request_id\n                WHERE 1=1 {where_conditions}\n                  AND pred.selected_deployment_id IS NOT NULL\n                GROUP BY m.name, pred.selected_deployment_id\n                ORDER BY m.name, total_requests DESC\n            ',
            "deployments_summary": '\n                SELECT \n                    pred.selected_deployment_id as deployment_id,\n                    COUNT(*) as total_requests,\n                    COUNT(CASE WHEN rl.success = true THEN 1 END) as successful_requests,\n                    ROUND((COUNT(CASE WHEN rl.success = true THEN 1 END) * 100.0 / COUNT(*))::numeric, 2) as success_rate,\n                    ROUND(AVG(rl.actual_response_time)::numeric, 3) as avg_response_time,\n                    ROUND(AVG(rl.prediction_accuracy)::numeric, 3) as avg_prediction_accuracy,\n                    COUNT(DISTINCT m.name) as unique_models,\n                    MIN(pred.created_at) as first_request,\n                    MAX(pred.created_at) as last_request\n                FROM "LiteLLM_PredictorLogs" pred\n                LEFT JOIN "LiteLLM_ResponseLogs" rl ON pred.request_id = rl.request_id\n                LEFT JOIN "LiteLLM_PromptLogs" pl ON pred.request_id = pl.request_id\n                LEFT JOIN "LiteLLM_Models" m ON pl.model_id = m.id\n                WHERE 1=1 {where_conditions}\n                  AND pred.selected_deployment_id IS NOT NULL\n                  AND m.name IS NOT NULL\n                GROUP BY pred.selected_deployment_id\n                ORDER BY total_requests DESC\n            ',
            "deployments_summary_full": '\n                SELECT \n                    pred.selected_deployment_id as deployment_internal_id,\n                    d.deployment_id,\n                    d.deployment_name,\n                    d.local_path,\n                    d.hardware_info,\n                    m.name as model_name,\n                    COUNT(*) as total_requests,\n                    COUNT(CASE WHEN rl.success = true THEN 1 END) as successful_requests,\n                    ROUND((COUNT(CASE WHEN rl.success = true THEN 1 END) * 100.0 / COUNT(*))::numeric, 2) as success_rate,\n                    ROUND(AVG(rl.actual_response_time)::numeric, 3) as avg_response_time,\n                    ROUND(AVG(rl.prediction_accuracy)::numeric, 3) as avg_prediction_accuracy,\n                    MIN(pred.created_at) as first_request,\n                    MAX(pred.created_at) as last_request\n                FROM "LiteLLM_PredictorLogs" pred\n                LEFT JOIN "LiteLLM_ResponseLogs" rl ON pred.request_id = rl.request_id\n                LEFT JOIN "LiteLLM_PromptLogs" pl ON pred.request_id = pl.request_id\n                LEFT JOIN "LiteLLM_Models" m ON pl.model_id = m.id\n                LEFT JOIN "LiteLLM_Deployments" d ON pred.selected_deployment_id = d.id\n                WHERE 1=1 {where_conditions}\n                  AND pred.selected_deployment_id IS NOT NULL\n                  AND m.name IS NOT NULL\n                GROUP BY pred.selected_deployment_id, d.deployment_id, d.deployment_name, d.local_path, d.hardware_info, m.name\n                ORDER BY pred.selected_deployment_id, total_requests DESC\n            ',
            "prompts_summary": '\n                SELECT \n                    COUNT(*) as total_prompts,\n                    COUNT(DISTINCT m.name) as unique_models,\n                    COUNT(DISTINCT pl.request_id) as unique_requests,\n                    COUNT(CASE WHEN pl.messages IS NOT NULL THEN 1 END) as prompts_with_text,\n                    ROUND(AVG(pl.prompt_length)::numeric, 0) as avg_prompt_length,\n                    ROUND(AVG(CASE WHEN pl.max_tokens IS NOT NULL THEN pl.max_tokens END)::numeric, 0) as avg_max_tokens,\n                    COUNT(CASE WHEN pl.stream = true THEN 1 END) as streaming_requests,\n                    MIN(pl.created_at) as earliest_prompt,\n                    MAX(pl.created_at) as latest_prompt,\n                    EXTRACT(EPOCH FROM (MAX(pl.created_at) - MIN(pl.created_at)))/3600 as time_span_hours\n                FROM "LiteLLM_PromptLogs" pl\n                LEFT JOIN "LiteLLM_Models" m ON pl.model_id = m.id\n                WHERE 1=1 {where_conditions}\n            ',
            "prompts_summary_full": '\n                SELECT \n                    m.name as model_name,\n                    pred.selected_deployment_id as endpoint,\n                    COUNT(*) as total_prompts,\n                    COUNT(CASE WHEN pl.messages IS NOT NULL THEN 1 END) as prompts_with_text,\n                    ROUND(AVG(pl.prompt_length)::numeric, 0) as avg_prompt_length,\n                    ROUND(AVG(CASE WHEN pl.max_tokens IS NOT NULL THEN pl.max_tokens END)::numeric, 0) as avg_max_tokens,\n                    COUNT(CASE WHEN pl.stream = true THEN 1 END) as streaming_requests,\n                    COUNT(CASE WHEN pl.temperature IS NOT NULL THEN 1 END) as requests_with_temperature,\n                    ROUND(AVG(CASE WHEN pl.temperature IS NOT NULL THEN pl.temperature END)::numeric, 2) as avg_temperature,\n                    COUNT(DISTINCT pl.user_id) as unique_users,\n                    MIN(pl.created_at) as first_prompt,\n                    MAX(pl.created_at) as last_prompt\n                FROM "LiteLLM_PromptLogs" pl\n                LEFT JOIN "LiteLLM_Models" m ON pl.model_id = m.id\n                LEFT JOIN "LiteLLM_PredictorLogs" pred ON pl.request_id = pred.request_id\n                WHERE 1=1 {where_conditions}\n                  AND pred.selected_deployment_id IS NOT NULL\n                GROUP BY m.name, pred.selected_deployment_id\n                ORDER BY m.name, total_prompts DESC\n            ',
            "predictors_summary": '\n                SELECT \n                    COUNT(*) as total_predictions,\n                    COUNT(DISTINCT selected_deployment_id) as unique_deployments,\n                    COUNT(CASE WHEN least_busy_available = true THEN 1 END) as least_busy_count,\n                    COUNT(CASE WHEN selected_hybrid_score > 0 THEN 1 END) as hybrid_score_count,\n                    ROUND(AVG(predicted_time)::numeric, 3) as avg_predicted_time,\n                    ROUND(MIN(predicted_time)::numeric, 3) as min_predicted_time,\n                    ROUND(MAX(predicted_time)::numeric, 3) as max_predicted_time,\n                    MIN(created_at) as earliest_prediction,\n                    MAX(created_at) as latest_prediction\n                FROM "LiteLLM_PredictorLogs"\n                WHERE 1=1 {where_conditions}\n            ',
            "predictors_summary_full": '\n                SELECT \n                    pred.selected_deployment_id as deployment_id,\n                    d.deployment_name,\n                    COUNT(*) as total_predictions,\n                    COUNT(CASE WHEN pred.least_busy_available = true THEN 1 END) as least_busy_count,\n                    COUNT(CASE WHEN pred.selected_hybrid_score > 0 THEN 1 END) as hybrid_score_count,\n                    ROUND(AVG(pred.predicted_time)::numeric, 3) as avg_predicted_time,\n                    ROUND(MIN(pred.predicted_time)::numeric, 3) as min_predicted_time,\n                    ROUND(MAX(pred.predicted_time)::numeric, 3) as max_predicted_time,\n                    ROUND(AVG(pred.selected_hybrid_score)::numeric, 4) as avg_hybrid_score,\n                    ROUND(AVG(pred.selected_current_load)::numeric, 1) as avg_current_load,\n                    ROUND(AVG(pred.analysis_duration_ms)::numeric, 0) as avg_analysis_duration_ms,\n                    COUNT(CASE WHEN pred.predictor_errors_count > 0 THEN 1 END) as predictions_with_errors,\n                    pred.selection_method,\n                    MIN(pred.created_at) as earliest_prediction,\n                    MAX(pred.created_at) as latest_prediction\n                FROM "LiteLLM_PredictorLogs" pred\n                LEFT JOIN "LiteLLM_Deployments" d ON pred.selected_deployment_id = d.id\n                WHERE 1=1 {where_conditions}\n                  AND pred.selected_deployment_id IS NOT NULL\n                GROUP BY pred.selected_deployment_id, d.deployment_name, pred.selection_method\n                ORDER BY pred.selected_deployment_id, total_predictions DESC\n            ',
            "responses_summary": '\n                SELECT \n                    COUNT(*) as total_responses,\n                    COUNT(CASE WHEN success = true THEN 1 END) as successful_responses,\n                    COUNT(CASE WHEN success = false THEN 1 END) as failed_responses,\n                    ROUND((COUNT(CASE WHEN success = true THEN 1 END) * 100.0 / COUNT(*))::numeric, 2) as success_rate,\n                    ROUND(AVG(actual_response_time)::numeric, 3) as avg_response_time,\n                    ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY actual_response_time)::numeric, 3) as p50_response_time,\n                    ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY actual_response_time)::numeric, 3) as p95_response_time,\n                    ROUND(PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY actual_response_time)::numeric, 3) as p99_response_time,\n                    ROUND(AVG(prediction_accuracy)::numeric, 3) as avg_prediction_accuracy,\n                    MIN(created_at) as earliest_response,\n                    MAX(created_at) as latest_response\n                FROM "LiteLLM_ResponseLogs"\n                WHERE 1=1 {where_conditions}\n            ',
            "responses_summary_full": '\n                SELECT \n                    rl.deployment_id,\n                    d.deployment_name,\n                    m.name as model_name,\n                    COUNT(*) as total_responses,\n                    COUNT(CASE WHEN rl.success = true THEN 1 END) as successful_responses,\n                    COUNT(CASE WHEN rl.success = false THEN 1 END) as failed_responses,\n                    ROUND((COUNT(CASE WHEN rl.success = true THEN 1 END) * 100.0 / COUNT(*))::numeric, 2) as success_rate,\n                    ROUND(AVG(rl.actual_response_time)::numeric, 3) as avg_response_time,\n                    ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY rl.actual_response_time)::numeric, 3) as p50_response_time,\n                    ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY rl.actual_response_time)::numeric, 3) as p95_response_time,\n                    ROUND(PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY rl.actual_response_time)::numeric, 3) as p99_response_time,\n                    ROUND(AVG(rl.time_to_first_token)::numeric, 3) as avg_time_to_first_token,\n                    ROUND(AVG(rl.stream_jitter)::numeric, 3) as avg_stream_jitter,\n                    ROUND(AVG(rl.prediction_accuracy)::numeric, 3) as avg_prediction_accuracy,\n                    ROUND(AVG(rl.tokens_per_second)::numeric, 2) as avg_tokens_per_second,\n                    MIN(rl.created_at) as earliest_response,\n                    MAX(rl.created_at) as latest_response\n                FROM "LiteLLM_ResponseLogs" rl\n                LEFT JOIN "LiteLLM_Deployments" d ON rl.deployment_id = d.id\n                LEFT JOIN "LiteLLM_PromptLogs" pl ON rl.request_id = pl.request_id\n                LEFT JOIN "LiteLLM_Models" m ON pl.model_id = m.id\n                WHERE 1=1 {where_conditions}\n                  AND rl.deployment_id IS NOT NULL\n                  AND m.name IS NOT NULL\n                GROUP BY rl.deployment_id, d.deployment_name, m.name\n                ORDER BY rl.deployment_id, total_responses DESC\n            ',
            "endpoints_summary": '\n                SELECT \n                    ea.deployment_id,\n                    d.deployment_name,\n                    COUNT(*) as total_analyses,\n                    ROUND(AVG(ea.predictor_response_time)::numeric, 3) as avg_predicted_time,\n                    ROUND(AVG(ea.current_load)::numeric, 1) as avg_current_load,\n                    ROUND(AVG(ea.hybrid_score)::numeric, 4) as avg_hybrid_score,\n                    COUNT(CASE WHEN ea.final_rank = 1 THEN 1 END) as times_ranked_first,\n                    MIN(ea.created_at) as earliest_analysis,\n                    MAX(ea.created_at) as latest_analysis\n                FROM "LiteLLM_EndpointAnalysis" ea\n                LEFT JOIN "LiteLLM_Deployments" d ON ea.deployment_id = d.id\n                WHERE 1=1 {where_conditions}\n                GROUP BY ea.deployment_id, d.deployment_name\n                ORDER BY times_ranked_first DESC\n            ',
            "overview_summary": '\n                SELECT \n                    \'Total System Activity\' as metric_category,\n                    COUNT(DISTINCT pl.request_id) as total_requests,\n                    COUNT(DISTINCT m.name) as unique_models,\n                    COUNT(DISTINCT pred.selected_deployment_id) as unique_deployments,\n                    ROUND((COUNT(CASE WHEN rl.success = true THEN 1 END) * 100.0 / COUNT(DISTINCT pl.request_id))::numeric, 2) as overall_success_rate,\n                    ROUND(AVG(rl.actual_response_time)::numeric, 3) as avg_response_time,\n                    ROUND(AVG(rl.prediction_accuracy)::numeric, 3) as avg_prediction_accuracy,\n                    MIN(pl.created_at) as system_start_time,\n                    MAX(pl.created_at) as last_activity_time\n                FROM "LiteLLM_PromptLogs" pl\n                LEFT JOIN "LiteLLM_Models" m ON pl.model_id = m.id\n                LEFT JOIN "LiteLLM_PredictorLogs" pred ON pl.request_id = pred.request_id\n                LEFT JOIN "LiteLLM_ResponseLogs" rl ON pl.request_id = rl.request_id\n                WHERE 1=1 {where_conditions}\n            ',
            "freshness_summary": "\n                SELECT \n                    'LiteLLM_PromptLogs' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_PromptLogs\"\n                WHERE 1=1 {where_conditions}\n                \n                UNION ALL\n                \n                SELECT \n                    'LiteLLM_PredictorLogs' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_PredictorLogs\"\n                WHERE 1=1 {where_conditions}\n                \n                UNION ALL\n                \n                SELECT \n                    'LiteLLM_ResponseLogs' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_ResponseLogs\"\n                WHERE 1=1 {where_conditions}\n                \n                UNION ALL\n                \n                SELECT \n                    'LiteLLM_EndpointAnalysis' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_EndpointAnalysis\"\n                WHERE 1=1 {where_conditions}\n                \n                ORDER BY table_name\n            ",
            "freshness_summary_full": "\n                SELECT \n                    'LiteLLM_Models' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_Models\"\n                WHERE 1=1 {where_conditions}\n                \n                UNION ALL\n                \n                SELECT \n                    'LiteLLM_Deployments' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_Deployments\"\n                WHERE 1=1 {where_conditions}\n                \n                UNION ALL\n                \n                SELECT \n                    'LiteLLM_PromptLogs' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_PromptLogs\"\n                WHERE 1=1 {where_conditions}\n                \n                UNION ALL\n                \n                SELECT \n                    'LiteLLM_PredictorLogs' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_PredictorLogs\"\n                WHERE 1=1 {where_conditions}\n                \n                UNION ALL\n                \n                SELECT \n                    'LiteLLM_ResponseLogs' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_ResponseLogs\"\n                WHERE 1=1 {where_conditions}\n                \n                UNION ALL\n                \n                SELECT \n                    'LiteLLM_EndpointAnalysis' as table_name,\n                    COUNT(*) as total_records,\n                    MIN(created_at) as oldest_record,\n                    MAX(created_at) as newest_record,\n                    EXTRACT(EPOCH FROM (NOW() - MAX(created_at)))/60 as minutes_since_last,\n                    EXTRACT(EPOCH FROM (MAX(created_at) - MIN(created_at)))/3600 as data_span_hours\n                FROM \"LiteLLM_EndpointAnalysis\"\n                WHERE 1=1 {where_conditions}\n                \n                ORDER BY table_name\n            ",
        }

    def get_template(self, name: str) -> str:
        """Get SQL template by name."""
        logger.debug(f"SUMMARY_QUERY_MANAGER: retrieving template: {name}")
        if name not in self.templates:
            logger.error(f"SUMMARY_QUERY_MANAGER: unknown SQL template: {name}")
            raise ValueError(f"Unknown SQL template: {name}")
        logger.debug(f"SUMMARY_QUERY_MANAGER: template {name} retrieved successfully")
        return self.templates[name]


class SummaryWhereClauseBuilder:
    """Safe WHERE clause construction for summary queries."""

    def __init__(self, template_name: str = ""):
        self.conditions = []
        self.template_name = template_name

    def add_time_condition(
        self, period_hours: Optional[int] = None, since: Optional[str] = None
    ) -> None:
        """Add time-based conditions."""
        # Determine which table's created_at to use based on template
        created_at_column = self._get_created_at_column()
        
        if since:
            escaped_since = since.replace("'", "''")
            self.conditions.append(f"{created_at_column} >= '{escaped_since}'")
        elif period_hours:
            self.conditions.append(
                f"{created_at_column} >= NOW() - INTERVAL '{period_hours}' HOUR"
            )

    def _get_created_at_column(self) -> str:
        """Get the appropriate created_at column based on template."""
        if "deployments_summary" in self.template_name:
            return "pred.created_at"
        elif "models_summary" in self.template_name:
            return "pl.created_at"
        elif "prompts_summary" in self.template_name:
            return "pl.created_at"
        elif "predictors_summary" in self.template_name:
            return "pred.created_at" if "predictors_summary_full" in self.template_name else "created_at"
        elif "responses_summary" in self.template_name:
            return "rl.created_at" if "responses_summary_full" in self.template_name else "created_at"
        elif "endpoints_summary" in self.template_name:
            return "ea.created_at"
        elif "overview_summary" in self.template_name:
            return "pl.created_at"
        elif "freshness_summary" in self.template_name:
            return "created_at"
        else:
            # Default fallback - should work for single-table queries
            return "created_at"

    def add_model_condition(self, model: Optional[str] = None) -> None:
        """Add model filter condition."""
        if model:
            escaped_model = model.replace("'", "''")
            self.conditions.append(f"m.name = '{escaped_model}'")

    def add_deployment_condition(self, deployment: Optional[str] = None) -> None:
        """Add deployment filter condition."""
        if deployment:
            escaped_deployment = deployment.replace("'", "''")
            self.conditions.append(f"selected_deployment_id = '{escaped_deployment}'")

    def build(self) -> str:
        """Build WHERE clause string."""
        if not self.conditions:
            return ""
        where_clause = " AND " + " AND ".join(self.conditions)
        logger.debug(f"SUMMARY_WHERE_BUILDER: built clause: {where_clause}")
        return where_clause


class SummaryQueryBuilder:
    """Query builder for summary reports."""

    def __init__(self, query_manager: SummaryQueryManager):
        self.query_manager = query_manager

    def build_query(self, template_name: str, filters: Dict[str, Any]) -> str:
        """Build complete SQL query with filters."""
        logger.debug(
            f"SUMMARY_QUERY_BUILDER: building query for template: {template_name}"
        )
        actual_template_name = self._resolve_template_name(
            template_name, filters.get("full", False)
        )
        template = self.query_manager.get_template(actual_template_name)
        where_builder = SummaryWhereClauseBuilder(actual_template_name)
        if filters.get("period_hours"):
            where_builder.add_time_condition(period_hours=filters["period_hours"])
        if filters.get("since"):
            where_builder.add_time_condition(since=filters["since"])
        if filters.get("model"):
            where_builder.add_model_condition(filters["model"])
        if filters.get("deployment"):
            where_builder.add_deployment_condition(filters["deployment"])
        where_clause = where_builder.build()
        final_query = template.replace("{where_conditions}", where_clause)
        logger.debug(
            f"SUMMARY_QUERY_BUILDER: query built successfully using template: {actual_template_name}"
        )
        return final_query

    def _resolve_template_name(self, base_template_name: str, is_full: bool) -> str:
        """Resolve template name based on --full flag."""
        full_templates = {
            "models_summary": "models_summary_full",
            "deployments_summary": "deployments_summary_full",
            "prompts_summary": "prompts_summary_full",
            "predictors_summary": "predictors_summary_full",
            "responses_summary": "responses_summary_full",
            "freshness_summary": "freshness_summary_full",
        }
        if is_full and base_template_name in full_templates:
            return full_templates[base_template_name]
        return base_template_name


class BaseSummaryFormatter(ABC):
    """Abstract base class for summary formatters."""

    @abstractmethod
    def format(self, data: List[Dict[str, Any]], metadata: Dict[str, Any]) -> str:
        """Format summary data."""
        pass


class TableSummaryFormatter(BaseSummaryFormatter):
    """Table formatter for summary reports."""

    def format(self, data: List[Dict[str, Any]], metadata: Dict[str, Any]) -> str:
        """Format data as ASCII table."""
        if not data:
            return "No data found for the specified criteria."
        title = metadata.get("title", "Summary Report")
        filters = metadata.get("filters", {})
        generated_at = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
        output = []
        output.append("=" * 80)
        output.append(f"  {title}")
        output.append("=" * 80)
        output.append(f"Generated: {generated_at}")
        if filters:
            output.append(f"Filters: {self._format_filters(filters)}")
        output.append("")
        headers = list(data[0].keys())
        col_widths = {}
        for header in headers:
            col_widths[header] = max(
                len(str(header)), max((len(str(row.get(header, ""))) for row in data))
            )
        header_row = " | ".join(
            (str(header).ljust(col_widths[header]) for header in headers)
        )
        separator = "-" * len(header_row)
        output.append(header_row)
        output.append(separator)
        for row in data:
            data_row = " | ".join(
                (
                    str(row.get(header, "")).ljust(col_widths[header])
                    for header in headers
                )
            )
            output.append(data_row)
        output.append("")
        output.append(f"Total rows: {len(data)}")
        output.append("=" * 80)
        return "\n".join(output)

    def _format_filters(self, filters: Dict[str, Any]) -> str:
        """Format filters for display."""
        filter_parts = []
        for key, value in filters.items():
            if value is not None:
                filter_parts.append(f"{key}={value}")
        return ", ".join(filter_parts) if filter_parts else "None"


class JsonSummaryFormatter(BaseSummaryFormatter):
    """JSON formatter for summary reports."""

    def format(self, data: List[Dict[str, Any]], metadata: Dict[str, Any]) -> str:
        """Format data as JSON."""
        result = {
            "metadata": metadata,
            "data": data,
            "generated_at": datetime.now(timezone.utc).isoformat(),
        }
        return json.dumps(result, indent=2, default=str)


class CsvSummaryFormatter(BaseSummaryFormatter):
    """CSV formatter for summary reports."""

    def format(self, data: List[Dict[str, Any]], metadata: Dict[str, Any]) -> str:
        """Format data as CSV."""
        if not data:
            return ""
        import csv
        import io

        output = io.StringIO()
        headers = list(data[0].keys())
        writer = csv.DictWriter(output, fieldnames=headers)
        writer.writeheader()
        writer.writerows(data)
        return output.getvalue()


class SummaryFormatterFactory:
    """Factory for creating summary formatters."""

    _formatters = {
        "table": TableSummaryFormatter,
        "json": JsonSummaryFormatter,
        "csv": CsvSummaryFormatter,
    }

    @classmethod
    def create(cls, format_type: str) -> BaseSummaryFormatter:
        """Create formatter instance."""
        if format_type not in cls._formatters:
            raise ValueError(f"Unknown format type: {format_type}")
        return cls._formatters[format_type]()


class SummaryInsightsEngine:
    """Generate insights for summary data."""

    def generate_insights(
        self, data: List[Dict[str, Any]], summary_type: str, filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate insights based on summary data."""
        logger.debug(f"SUMMARY_INSIGHTS: generating insights for {summary_type}")
        if not data:
            return {
                "summary": "No data available for analysis",
                "insights": [],
                "recommendations": [],
                "status": "no_data",
            }
        try:
            if summary_type in ["models_summary", "models_summary_full"]:
                return self._analyze_models_summary(data)
            elif summary_type in ["deployments_summary", "deployments_summary_full"]:
                return self._analyze_deployments_summary(data)
            elif summary_type in ["prompts_summary", "prompts_summary_full"]:
                return self._analyze_prompts_summary(data)
            elif summary_type in ["predictors_summary", "predictors_summary_full"]:
                return self._analyze_predictors_summary(data)
            elif summary_type in ["responses_summary", "responses_summary_full"]:
                return self._analyze_responses_summary(data)
            elif summary_type == "overview_summary":
                return self._analyze_overview_summary(data)
            elif summary_type in ["freshness_summary", "freshness_summary_full"]:
                return self._analyze_freshness_summary(data)
            else:
                return self._generate_generic_insights(data, summary_type)
        except Exception as e:
            logger.error(f"SUMMARY_INSIGHTS: error generating insights: {e}")
            return {
                "summary": f"Error generating insights: {str(e)}",
                "insights": [],
                "recommendations": [],
                "status": "error",
            }

    def _analyze_models_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze models summary data."""
        total_models = len(data)
        total_requests = sum((row.get("total_requests", 0) for row in data))
        avg_success_rate = (
            sum((row.get("success_rate", 0) for row in data)) / total_models
            if total_models > 0
            else 0
        )
        insights = [
            f"System has {total_models} active models",
            f"Total requests processed: {total_requests:,}",
            f"Average success rate across models: {avg_success_rate:.2f}%",
        ]
        if data:
            top_model = max(data, key=lambda x: x.get("total_requests", 0))
            insights.append(
                f"Most active model: {top_model.get('model_name')} ({top_model.get('total_requests')} requests)"
            )
        recommendations = []
        if avg_success_rate < 95:
            recommendations.append(
                "Consider investigating models with low success rates"
            )
        return {
            "summary": f"Models summary for {total_models} models with {total_requests:,} total requests",
            "insights": insights,
            "recommendations": recommendations,
            "status": "success",
        }

    def _analyze_deployments_summary(
        self, data: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Analyze deployments summary data."""
        total_deployments = len(data)
        total_requests = sum((row.get("total_requests", 0) for row in data))
        avg_success_rate = (
            sum((row.get("success_rate", 0) for row in data)) / total_deployments
            if total_deployments > 0
            else 0
        )
        insights = [
            f"System has {total_deployments} active deployments",
            f"Total requests processed: {total_requests:,}",
            f"Average success rate across deployments: {avg_success_rate:.2f}%",
        ]
        recommendations = []
        if total_deployments < 2:
            recommendations.append(
                "Consider adding more deployments for better load distribution"
            )
        return {
            "summary": f"Deployments summary for {total_deployments} deployments",
            "insights": insights,
            "recommendations": recommendations,
            "status": "success",
        }

    def _analyze_overview_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze system overview data."""
        if not data:
            return {
                "summary": "No overview data",
                "insights": [],
                "recommendations": [],
                "status": "no_data",
            }
        overview = data[0]
        insights = [
            f"Total requests: {overview.get('total_requests', 0):,}",
            f"Unique models: {overview.get('unique_models', 0)}",
            f"Unique deployments: {overview.get('unique_deployments', 0)}",
            f"Overall success rate: {overview.get('overall_success_rate', 0)}%",
            f"Average response time: {overview.get('avg_response_time', 0):.3f}s",
        ]
        recommendations = []
        success_rate = overview.get("overall_success_rate", 0)
        avg_response_time = overview.get("avg_response_time", 0)
        if success_rate < 95:
            recommendations.append(
                "System success rate is below 95% - investigate failures"
            )
        if avg_response_time > 5.0:
            recommendations.append(
                "Average response time is high - consider optimization"
            )
        return {
            "summary": "System overview analysis",
            "insights": insights,
            "recommendations": recommendations,
            "status": "success",
        }

    def _analyze_prompts_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze prompts summary data."""
        if not data:
            return {
                "summary": "No prompt data",
                "insights": [],
                "recommendations": [],
                "status": "no_data",
            }
        if len(data) == 1 and "endpoint" not in data[0]:
            prompt_data = data[0]
            total_prompts = prompt_data.get("total_prompts", 0)
            unique_models = prompt_data.get("unique_models", 0)
            streaming_requests = prompt_data.get("streaming_requests", 0)
            avg_prompt_length = prompt_data.get("avg_prompt_length", 0)
            insights = [
                f"Total prompts: {total_prompts:,}",
                f"Unique models: {unique_models}",
                f"Average prompt length: {avg_prompt_length} characters",
                (
                    f"Streaming requests: {streaming_requests:,} ({streaming_requests / total_prompts * 100:.1f}%)"
                    if total_prompts > 0
                    else "Streaming requests: 0"
                ),
            ]
        else:
            total_prompts = sum((row.get("total_prompts", 0) for row in data))
            unique_combinations = len(data)
            insights = [
                f"Total prompts: {total_prompts:,} across {unique_combinations} model-endpoint combinations"
            ]
            top_pairs = sorted(
                data, key=lambda x: x.get("total_prompts", 0), reverse=True
            )[:3]
            if top_pairs:
                insights.append("Most active model-endpoint pairs:")
                for pair in top_pairs:
                    model = pair.get("model_name", "Unknown")
                    endpoint = pair.get("endpoint", "Unknown")
                    count = pair.get("total_prompts", 0)
                    insights.append(f"  • {model}/{endpoint}: {count:,} prompts")
            temp_usage = [
                row for row in data if row.get("requests_with_temperature", 0) > 0
            ]
            if temp_usage:
                insights.append(
                    f"Temperature parameter used in {len(temp_usage)}/{len(data)} combinations"
                )
        recommendations = []
        if len(data) > 1:
            prompt_counts = [row.get("total_prompts", 0) for row in data]
            if max(prompt_counts) > 10 * min(prompt_counts) and min(prompt_counts) > 0:
                recommendations.append(
                    "Highly imbalanced prompt distribution detected - consider load balancing review"
                )
        return {
            "summary": f"Prompts analysis for {len(data)} entries",
            "insights": insights,
            "recommendations": recommendations,
            "status": "success",
        }

    def _analyze_predictors_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze predictors summary data."""
        if not data:
            return {
                "summary": "No predictor data",
                "insights": [],
                "recommendations": [],
                "status": "no_data",
            }
        if len(data) == 1 and "deployment_name" not in data[0]:
            predictor_data = data[0]
            total_predictions = predictor_data.get("total_predictions", 0)
            unique_deployments = predictor_data.get("unique_deployments", 0)
            avg_predicted_time = predictor_data.get("avg_predicted_time", 0)
            insights = [
                f"Total predictions: {total_predictions:,}",
                f"Unique deployments: {unique_deployments}",
                f"Average predicted time: {avg_predicted_time:.3f}s",
                f"Min predicted time: {predictor_data.get('min_predicted_time', 0):.3f}s",
                f"Max predicted time: {predictor_data.get('max_predicted_time', 0):.3f}s",
            ]
        else:
            total_predictions = sum((row.get("total_predictions", 0) for row in data))
            unique_deployments = len(data)
            avg_predicted_time = (
                sum((row.get("avg_predicted_time", 0) for row in data)) / len(data)
                if data
                else 0
            )
            insights = [
                f"Total predictions: {total_predictions:,} across {unique_deployments} deployments",
                f"Average predicted time: {avg_predicted_time:.3f}s",
            ]
            best_deployment = min(
                data, key=lambda x: x.get("avg_predicted_time", float("inf"))
            )
            worst_deployment = max(data, key=lambda x: x.get("avg_predicted_time", 0))
            if best_deployment and worst_deployment:
                best_name = best_deployment.get("deployment_name", "Unknown")
                best_time = best_deployment.get("avg_predicted_time", 0)
                worst_name = worst_deployment.get("deployment_name", "Unknown")
                worst_time = worst_deployment.get("avg_predicted_time", 0)
                insights.append(
                    f"Fastest deployment: {best_name} ({best_time:.3f}s avg)"
                )
                insights.append(
                    f"Slowest deployment: {worst_name} ({worst_time:.3f}s avg)"
                )
        recommendations = []
        if any((row.get("predictions_with_errors", 0) > 0 for row in data)):
            recommendations.append(
                "Some deployments have prediction errors - check predictor service health"
            )
        if len(data) > 1:
            prediction_counts = [row.get("total_predictions", 0) for row in data]
            if (
                max(prediction_counts) > 5 * min(prediction_counts)
                and min(prediction_counts) > 0
            ):
                recommendations.append(
                    "Uneven prediction distribution across deployments - review load balancing"
                )
        return {
            "summary": f"Predictors analysis for {len(data)} entries",
            "insights": insights,
            "recommendations": recommendations,
            "status": "success",
        }

    def _analyze_responses_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze responses summary data."""
        if not data:
            return {
                "summary": "No response data",
                "insights": [],
                "recommendations": [],
                "status": "no_data",
            }
        if len(data) == 1 and "endpoint" not in data[0]:
            response_data = data[0]
            total_responses = response_data.get("total_responses", 0)
            success_rate = response_data.get("success_rate", 0)
            avg_response_time = response_data.get("avg_response_time", 0)
            insights = [
                f"Total responses: {total_responses:,}",
                f"Success rate: {success_rate}%",
                f"Average response time: {avg_response_time:.3f}s",
                f"P95 response time: {response_data.get('p95_response_time', 0):.3f}s",
            ]
        else:
            total_responses = sum((row.get("total_responses", 0) for row in data))
            avg_success_rate = (
                sum((row.get("success_rate", 0) for row in data)) / len(data)
                if data
                else 0
            )
            insights = [
                f"Total responses: {total_responses:,} across {len(data)} endpoint-model combinations",
                f"Average success rate: {avg_success_rate:.2f}%",
            ]
            worst_performers = sorted(data, key=lambda x: x.get("success_rate", 100))[
                :3
            ]
            if worst_performers and worst_performers[0].get("success_rate", 100) < 95:
                insights.append("Worst performing endpoint-model pairs:")
                for performer in worst_performers:
                    endpoint = performer.get("endpoint", "Unknown")
                    model = performer.get("model_name", "Unknown")
                    rate = performer.get("success_rate", 0)
                    insights.append(f"  • {endpoint}/{model}: {rate}% success rate")
        recommendations = []
        if "success_rate" in data[0] and any(
            (row.get("success_rate", 100) < 95 for row in data)
        ):
            recommendations.append(
                "Some endpoints have success rates below 95% - investigate failures"
            )
        return {
            "summary": f"Response analysis for {len(data)} entries",
            "insights": insights,
            "recommendations": recommendations,
            "status": "success",
        }

    def _analyze_freshness_summary(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze data freshness."""
        insights = []
        recommendations = []
        for table_data in data:
            table_name = table_data.get("table_name", "Unknown")
            minutes_since_last = table_data.get("minutes_since_last", 0)
            total_records = table_data.get("total_records", 0)
            insights.append(
                f"{table_name}: {total_records:,} records, last activity {minutes_since_last:.1f} minutes ago"
            )
            if minutes_since_last > 60:
                recommendations.append(
                    f"{table_name} has not been updated in over an hour"
                )
        return {
            "summary": "Data freshness analysis",
            "insights": insights,
            "recommendations": recommendations,
            "status": "success",
        }

    def _generate_generic_insights(
        self, data: List[Dict[str, Any]], summary_type: str
    ) -> Dict[str, Any]:
        """Generate generic insights for other summary types."""
        record_count = len(data)
        return {
            "summary": f"{summary_type.replace('_', ' ').title()} with {record_count} records",
            "insights": [f"Found {record_count} records in summary"],
            "recommendations": [],
            "status": "success",
        }


class AnalyticsSummaryGenerator:
    """Main class for generating analytics summaries."""

    def __init__(self, db_client: Optional[AnalyticsPrismaClient] = None):
        """Initialize summary generator."""
        logger.debug("SUMMARY_GENERATOR: initializing analytics summary generator")
        self.db_client = db_client or get_analytics_prisma_client()
        self.query_manager = SummaryQueryManager()
        self.query_builder = SummaryQueryBuilder(self.query_manager)
        self.insights_engine = SummaryInsightsEngine()
        logger.info("SUMMARY_GENERATOR: initialization completed successfully")

    async def generate_summary(
        self,
        summary_type: str,
        filters: Dict[str, Any],
        format_type: str = "table",
        output_file: Optional[str] = None,
    ) -> bool:
        """Generate summary report."""
        logger.info(
            f"SUMMARY_GENERATOR: starting {summary_type} generation in {format_type} format"
        )
        try:
            query = self.query_builder.build_query(summary_type, filters)
            logger.debug(f"SUMMARY_GENERATOR: executing query for {summary_type}")
            results = await self.db_client.query_raw(query)
            logger.info(f"SUMMARY_GENERATOR: query returned {len(results)} records")
            insights = self.insights_engine.generate_insights(
                results, summary_type, filters
            )
            metadata = {
                "title": f"{summary_type.replace('_', ' ').title()}",
                "summary_type": summary_type,
                "filters": filters,
                "record_count": len(results),
                "insights": insights,
            }
            formatter = SummaryFormatterFactory.create(format_type)
            formatted_output = formatter.format(results, metadata)
            if format_type == "table" and insights.get("insights"):
                formatted_output += "\n\nINSIGHTS:\n"
                for insight in insights["insights"]:
                    formatted_output += f"• {insight}\n"
                if insights.get("recommendations"):
                    formatted_output += "\nRECOMMENDATIONS:\n"
                    for rec in insights["recommendations"]:
                        formatted_output += f"• {rec}\n"
            if output_file:
                with open(output_file, "w") as f:
                    f.write(formatted_output)
                logger.info(f"SUMMARY_GENERATOR: output written to {output_file}")
            else:
                print(formatted_output)
            logger.info(
                f"SUMMARY_GENERATOR: {summary_type} generation completed successfully"
            )
            return True
        except Exception as e:
            logger.error(f"SUMMARY_GENERATOR: error generating {summary_type}: {e}")
            logger.debug(
                f"SUMMARY_GENERATOR: error traceback: {traceback.format_exc()}"
            )
            return False


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Analytics Summary Generator")
    parser.add_argument(
        "summary_type",
        choices=[
            "models",
            "deployments",
            "prompts",
            "predictors",
            "responses",
            "endpoints",
            "overview",
            "freshness",
            "all",
        ],
        help="Type of summary to generate",
    )
    parser.add_argument(
        "--format",
        choices=["table", "json", "csv"],
        default="table",
        help="Output format (default: table)",
    )
    parser.add_argument("--output", help="Output file path (default: stdout)")
    parser.add_argument("--period-hours", type=int, help="Filter data to last N hours")
    parser.add_argument("--since", help="Filter data since timestamp (ISO format)")
    parser.add_argument("--model", help="Filter by specific model name")
    parser.add_argument("--deployment", help="Filter by specific deployment ID")
    parser.add_argument(
        "--full",
        action="store_true",
        help="Include full details in summary (affects models, deployments, responses, freshness)",
    )
    return parser.parse_args()


async def main():
    """Main entry point."""
    try:
        args = parse_arguments()
        filters = {}
        if args.period_hours:
            filters["period_hours"] = args.period_hours
        if args.since:
            filters["since"] = args.since
        if args.model:
            filters["model"] = args.model
        if args.deployment:
            filters["deployment"] = args.deployment
        if args.full:
            filters["full"] = True
        generator = AnalyticsSummaryGenerator()
        if args.summary_type == "all":
            summary_types = [
                "models",
                "deployments",
                "prompts",
                "predictors",
                "responses",
                "endpoints",
                "overview",
                "freshness",
            ]
            all_success = True
            for summary_type in summary_types:
                logger.info(f"MAIN: generating {summary_type} summary")
                output_file = None
                if args.output:
                    base_name, ext = os.path.splitext(args.output)
                    output_file = f"{base_name}_{summary_type}{ext}"
                success = await generator.generate_summary(
                    f"{summary_type}_summary", filters, args.format, output_file
                )
                if not success:
                    all_success = False
                    logger.error(f"MAIN: failed to generate {summary_type} summary")
                if not output_file and summary_type != summary_types[-1]:
                    print("\n" + "=" * 80 + "\n")
            if all_success:
                logger.info("MAIN: all summaries generated successfully")
                sys.exit(0)
            else:
                logger.error("MAIN: some summaries failed to generate")
                sys.exit(1)
        else:
            success = await generator.generate_summary(
                f"{args.summary_type}_summary", filters, args.format, args.output
            )
            if success:
                logger.info("MAIN: summary generation completed successfully")
                sys.exit(0)
            else:
                logger.error("MAIN: summary generation failed")
                sys.exit(1)
    except KeyboardInterrupt:
        logger.warning("MAIN: operation cancelled by user")
        sys.exit(130)
    except Exception as e:
        logger.error(f"MAIN: unexpected error: {e}")
        logger.debug(f"MAIN: error traceback: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
